<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="ch">
<head>
    <base th:href="${#request.getScheme()}+'://'+${#request.getServerName()}+':'+${#request.getServerPort()}+${#request.getContextPath()}">
    <meta charset="UTF-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公告列表</title>
    <link rel="stylesheet" type="text/css" th:href="@{/static/layui/css/layui.css}"/>
    <script th:inline="javascript">
        var basePath = [[${#httpServletRequest.getScheme() + "://" + #httpServletRequest.getServerName() + ":" + #httpServletRequest.getServerPort() + #httpServletRequest.getContextPath()}]];
    </script>
</head>
<body style="margin:10px;background-color: white">
<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-sm-12">
            <div class="animated fadeIn">
                <table class="layui-hide" id="newslist" lay-filter="test" style="width:100%"></table>
                <script type="text/html" id="barDemo">
                    <a class="layui-btn layui-btn-xs" lay-event="xiangqing">详情</a>
                    <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="bianji">编辑</a>
                    <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="shanchu">删除公告</a>
                </script>
            </div>
        </div>
    </div>
</div>
</body>
<script th:src="@{/static/layui/jquery.min.js}"></script>
<script th:src="@{/static/layui/layui.js}"></script>
<script th:src="@{/static/js/admin/newslist.js}"></script>
</html>