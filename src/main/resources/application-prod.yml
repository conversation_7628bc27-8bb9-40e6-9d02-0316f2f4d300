spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ******************************************************************************
    username: root
    password: 123456
    #指定数据源
    type: com.alibaba.druid.pool.DruidDataSource

    # 数据源其他配置
    tomcat:
      max-active: 20
      max-wait: 60000
    dbcp2:
      initial-size: 5
      min-idle: 5
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
    #   配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall' 用于防火墙
    filters: stat,log4j
    maxPoolPreparedStatementPerConnectionSize: 20
    useGlobalDataSourceStat: true
    connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=500

  thymeleaf:
    suffix: .html
    prefix:
      classpath: /templates/
    cache: false

  jackson: #返回的日期字段的格式
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false # true 使用时间戳显示时间
  http:
    multipart:
      max-file-size: 1000Mb
      max-request-size: 1000Mb
#配置文件式开发
mybatis:
  #全局配置文件的位置
  config-location: classpath:mybatis/mybatis-config.xml
  #所有sql映射配置文件的位置
  mapper-locations: classpath:mybatis/mapper/**/*.xml
  #开启MyBatis的二级缓存
  configuration:
    cache-enabled: true

#控制台看到执行的SQL语句
logging:
  level:
    com:
      mapper: debug

server:
  port: 8088
  session:
    timeout: 7200