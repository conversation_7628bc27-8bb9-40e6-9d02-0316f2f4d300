package com.itheima.utils;


import io.minio.*;
import io.minio.http.Method;
import io.minio.messages.Bucket;
import io.minio.messages.Item;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/04/05
 */
public class MinioUtil {
    private static String ENDPOINT;
    private static String ACCESS_KEY;

    /**
     * secretKey 是你账户的密码，至少8位
     */
    private static String SECRET_KEY;

    /**
     * 默认存储桶
     */
    public static String BUCKET_NAME;

    private static final MinioClient MINIO_CLIENT = MinioClient.builder()
            .endpoint(ENDPOINT)
            .credentials(ACCESS_KEY, SECRET_KEY)
            .build();
    
    /**
     * 启动springBoot容器的时候初始化Bucket
     * 如果没有Bucket则创建
     *
     * @param bucketName 存储桶
     */
    public void createBucket(String bucketName) {
        try {
            if (!bucketExists(bucketName)) {
                MINIO_CLIENT.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
                System.out.println("创建bucketName = " + bucketName + "完成!");
                return;
            }
            System.out.println("bucketName = " + bucketName + "已存在! 策略为: " + getBucketPolicy(bucketName));
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }

    /**
     * 判断Bucket是否存在
     *
     * @param bucketName 存储桶
     * @return boolean
     */
    @SneakyThrows
    public boolean bucketExists(String bucketName) {
        return MINIO_CLIENT.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
    }

    /**
     * 获得Bucket的策略
     *
     * @param bucketName 存储桶
     * @return {@link String}
     */
    @SneakyThrows
    public String getBucketPolicy(String bucketName) {
        return MINIO_CLIENT.getBucketPolicy(GetBucketPolicyArgs.builder().bucket(bucketName).build());
    }

    /**
     * 获得所有Bucket列表
     *
     * @return {@link List}<{@link Bucket}>
     */
    @SneakyThrows
    public List<Bucket> getAllBuckets() {
        return MINIO_CLIENT.listBuckets();
    }

    /**
     * 根据bucketName获取其相关信息
     *
     * @param bucketName 存储桶
     * @return {@link Optional}<{@link Bucket}>
     */
    @SneakyThrows(Exception.class)
    public Optional<Bucket> getBucket(String bucketName) {
        return getAllBuckets().stream().filter(b -> b.name().equals(bucketName)).findFirst();
    }

    /**
     * 根据bucketName删除Bucket
     *
     * @param bucketName 存储桶
     */
    @SneakyThrows(Exception.class)
    public void removeBucket(String bucketName) {
        MINIO_CLIENT.removeBucket(RemoveBucketArgs.builder().bucket(bucketName).build());
    }

    /**
     * 判断文件是否存在
     *
     * @param bucketName 存储桶
     * @param objectName 文件名
     * @return boolean
     */
    public boolean isObjectExists(String bucketName, String objectName) {
        boolean exist = true;
        try {
            MINIO_CLIENT.statObject(StatObjectArgs.builder().bucket(bucketName).object(objectName).build());
        } catch (Exception e) {
            System.out.println("[Minio工具类]>>>> 判断文件是否存在，异常: " + e.getMessage());
            exist = false;
        }
        return exist;
    }

    /**
     * 判断文件夹是否存在
     *
     * @param bucketName 存储桶
     * @param objectName 文件夹名
     * @return boolean
     */
    public boolean isFolderExists(String bucketName, String objectName) {
        boolean exist = false;
        try {
            Iterable<Result<Item>> results = MINIO_CLIENT.listObjects(ListObjectsArgs.builder().bucket(bucketName).prefix(objectName).recursive(false).build());
            for (Result<Item> result : results) {
                Item item = result.get();
                if (item.isDir() && objectName.equals(item.objectName())) {
                    exist = true;
                }
            }
        } catch (Exception e) {
            System.out.println("[Minio工具类]>>>> 判断文件夹是否存在，异常：" + e.getMessage());
            exist = false;
        }
        return exist;
    }

    /**
     * 根据文件前置查询文件
     *
     * @param bucketName 存储桶
     * @param prefix     前缀
     * @param recursive  是否使用递归查询
     * @return {@link List}<{@link Item}>  列表
     */
    @SneakyThrows(Exception.class)
    public List<Item> getAllObjectsByPrefix(String bucketName, String prefix, boolean recursive) {
        List<Item> list = new ArrayList<>();
        Iterable<Result<Item>> objectsIterator = MINIO_CLIENT.listObjects(ListObjectsArgs.builder().bucket(bucketName).prefix(prefix).recursive(recursive).build());
        if (objectsIterator != null) {
            for (Result<Item> o : objectsIterator) {
                Item item = o.get();
                list.add(item);
            }
        }
        return list;
    }

    /**
     * 获取文件流
     *
     * @param bucketName 存储桶
     * @param objectName 文件名
     * @return {@link InputStream} 二进制流
     */
    @SneakyThrows(Exception.class)
    public InputStream getObject(String bucketName, String objectName) {
        return MINIO_CLIENT.getObject(GetObjectArgs.builder().bucket(bucketName).object(objectName).build());
    }

    /**
     * 断点下载
     *
     * @param bucketName 存储桶
     * @param objectName 文件名称
     * @param offset     起始字节的位置
     * @param length     要读取的长度
     * @return {@link InputStream} 二进制流
     */
    @SneakyThrows(Exception.class)
    public InputStream getObject(String bucketName, String objectName, long offset, long length) {
        return MINIO_CLIENT.getObject(GetObjectArgs.builder().bucket(bucketName).object(objectName).offset(offset).length(length).build());
    }

    /**
     * 获取路径下文件列表
     *
     * @param bucketName 存储桶
     * @param prefix     文件名称
     * @param recursive  是否递归查找，false：模拟文件夹结构查找
     * @return {@link Iterable}<{@link Result}<{@link Item}>>
     */
    public Iterable<Result<Item>> listObjects(String bucketName, String prefix, boolean recursive) {
        return MINIO_CLIENT.listObjects(ListObjectsArgs.builder().bucket(bucketName).prefix(prefix).recursive(recursive).build());
    }

    /**
     * 使用MultipartFile进行文件上传
     *
     * @param bucketName  存储桶
     * @param file        文件名
     * @param objectName  对象名
     * @param contentType 类型
     */
    @SneakyThrows(Exception.class)
    public static void uploadFile(String bucketName, MultipartFile file, String objectName, String contentType) {
        InputStream inputStream = file.getInputStream();
        MINIO_CLIENT.putObject(PutObjectArgs.builder().bucket(bucketName).object(objectName).contentType(contentType).stream(inputStream, inputStream.available(), -1).build());
    }

    /**
     * 图片上传
     *
     * @param bucketName  存储桶
     * @param imageBase64 图片的base64码
     * @param imageName   图片名字
     * @return {@link ObjectWriteResponse}
     */
    public ObjectWriteResponse uploadImage(String bucketName, String imageBase64, String imageName) {
        if (!StringUtils.hasLength(imageBase64)) {
            return null;
        }
        InputStream in = base64ToInputStream(imageBase64);
        String newName = System.currentTimeMillis() + "_" + imageName;
        return uploadFile(bucketName, newName, in);
    }

    /**
     * 将base64转为bytes
     *
     * @param base64 base64码
     * @return {@link InputStream}
     */
    public static InputStream base64ToInputStream(String base64) {
        ByteArrayInputStream stream = null;
        try {
            byte[] bytes = Base64.getEncoder().encode(base64.trim().getBytes());
            stream = new ByteArrayInputStream(bytes);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        return stream;
    }

    /**
     * 上传本地文件
     *
     * @param bucketName 存储桶
     * @param objectName 对象名称
     * @param fileName   本地文件路径
     * @return {@link ObjectWriteResponse}
     */
    @SneakyThrows(Exception.class)
    public ObjectWriteResponse uploadFile(String bucketName, String objectName, String fileName) {
        return MINIO_CLIENT.uploadObject(UploadObjectArgs.builder().bucket(bucketName).object(objectName).filename(fileName).build());
    }

    /**
     * 通过流上传文件
     *
     * @param bucketName  存储桶
     * @param objectName  文件对象
     * @param inputStream 文件流
     * @return {@link ObjectWriteResponse}
     */
    @SneakyThrows(Exception.class)
    public ObjectWriteResponse uploadFile(String bucketName, String objectName, InputStream inputStream) {
        return MINIO_CLIENT.putObject(PutObjectArgs.builder().bucket(bucketName).object(objectName).stream(inputStream, inputStream.available(), -1).build());
    }

    /**
     * 创建文件夹或目录
     *
     * @param bucketName 存储桶
     * @param objectName 目录路径
     * @return {@link ObjectWriteResponse}
     */
    @SneakyThrows(Exception.class)
    public ObjectWriteResponse createDir(String bucketName, String objectName) {
        return MINIO_CLIENT.putObject(PutObjectArgs.builder().bucket(bucketName).object(objectName).stream(new ByteArrayInputStream(new byte[]{}), 0, -1).build());
    }

    /**
     * 获取文件信息，如果抛出异常则说明文件不存在
     *
     * @param bucketName 存储桶
     * @param objectName 文件名称
     * @return {@link String}
     */
    @SneakyThrows(Exception.class)
    public String getFileStatusInfo(String bucketName, String objectName) {
        return MINIO_CLIENT.statObject(StatObjectArgs.builder().bucket(bucketName).object(objectName).build()).toString();
    }

    /**
     * 拷贝文件
     *
     * @param bucketName    存储桶
     * @param objectName    文件名
     * @param srcBucketName 目标存储桶
     * @param srcObjectName 目标文件名
     * @return {@link ObjectWriteResponse}
     */
    @SneakyThrows(Exception.class)
    public ObjectWriteResponse copyFile(String bucketName, String objectName, String srcBucketName, String srcObjectName) {
        return MINIO_CLIENT.copyObject(CopyObjectArgs.builder().source(CopySource.builder().bucket(bucketName).object(objectName).build()).bucket(srcBucketName).object(srcBucketName).build());
    }

    /**
     * 删除文件
     *
     * @param bucketName 存储桶
     * @param objectName 文件名称
     */
    @SneakyThrows(Exception.class)
    public void removeFile(String bucketName, String objectName) {
        MINIO_CLIENT.removeObject(RemoveObjectArgs.builder().bucket(bucketName).object(objectName).build());
    }

    /**
     * 获取文件外链
     *
     * @param bucketName 存储桶
     * @param objectName 文件名
     * @param expires    过期时间  <= 7秒  (外链有效时间 (单位: 秒))
     * @return {@link String}
     */
    @SneakyThrows(Exception.class)
    public String getPresignedObjectUrl(String bucketName, String objectName, Integer expires) {
        return MINIO_CLIENT.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder().expiry(expires).bucket(bucketName).object(objectName).build());
    }

    /**
     * 获取文件外链
     *
     * @param bucketName 存储桶
     * @param objectName 文件名
     * @return {@link String}
     */
    @SneakyThrows(Exception.class)
    public static String getPresignedObjectUrl(String bucketName, String objectName) {
        return MINIO_CLIENT.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder().bucket(bucketName).object(objectName).method(Method.GET).build());
    }

    /**
     * 将URLDecoder编码转成UTF8
     *
     * @param str URLDecoder编码
     * @return {@link String}
     */
    public String getUtf8ByUrlDecoder(String str) throws UnsupportedEncodingException {
        String url = str.replaceAll("%(?![0-9a-fA-F]{2})", "%25");
        return URLDecoder.decode(url, StandardCharsets.UTF_8);
    }
}