package com.itheima.controller;

import com.itheima.pojo.Result;
import com.itheima.pojo.User;
import com.itheima.service.UserService;
import com.itheima.utils.JwtUtil;
import com.itheima.utils.Md5Util;
import com.itheima.utils.ThreadLocalUtil;
import jakarta.validation.constraints.Pattern;
import org.hibernate.validator.constraints.URL;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/04/04
 */
@Validated
@RestController
@RequestMapping("/user")
@CrossOrigin
public class UserController {

    private final UserService userService;
    private final StringRedisTemplate stringRedisTemplate;
    public UserController(UserService userService, StringRedisTemplate stringRedisTemplate) {
        this.userService = userService;
        this.stringRedisTemplate = stringRedisTemplate;
    }

    @RequestMapping(value = "/register", method = RequestMethod.POST)
    public Result<String> register(@Pattern(regexp = "^\\S{5,16}$") String username, @Pattern(regexp = "^\\S{5,16}$") String password) {
        // 查询用户
        if (userService.findByUserName(username) != null) {
            return Result.error("该用户名已存在");
        }
        // 注册
        userService.register(username, password);
        return Result.success();
    }

    @RequestMapping(value = "/login", method = RequestMethod.POST)
    public Result<String> login(@Pattern(regexp = "^\\S{5,16}$") String username, @Pattern(regexp = "^\\S{5,16}$") String password) {
        // 根据用户名查询用户
        User loginUser = userService.findByUserName(username);
        // 判断该用户是否存在
        if (loginUser == null) {
            return Result.error("用户名错误");
        }
        // 判断密码是否正确 loginUser对象中的password是密文
        if (Md5Util.getMd5String(password).equals(loginUser.getPassword())) {
            // 登录成功
            Map<String, Object> claims = new HashMap<>();
            claims.put("id", loginUser.getId());
            claims.put("username", username);
            String token = JwtUtil.genToken(claims);
            // 把token存储到redis中
            ValueOperations<String, String> operations = stringRedisTemplate.opsForValue();
            operations.set(token, token, 1, TimeUnit.HOURS);
            return Result.success(token);
        }
        return Result.error("密码错误");
    }

    @RequestMapping(value = "/logout", method = RequestMethod.POST)
    public Result<String> logout() {
        Map<String, String> claims = ThreadLocalUtil.get();
        System.out.println(claims);
//        if (key != null) {
//            stringRedisTemplate.opsForValue().getOperations().delete(key);
//        }
        return Result.success("退出成功");
    }

    @RequestMapping(value = "/userInfo", method = RequestMethod.GET)
    public Result<User> userInfo() {
        // 根据用户名查询用户
        Map<String, Object> claims = ThreadLocalUtil.get();
        return Result.success(userService.findByUserName((String) claims.get("username")));
    }

    @RequestMapping(value = "/update", method = RequestMethod.PUT)
    public Result<String> update(@RequestBody @Validated User user) {
        if (userService.update(user)) {
            return Result.success();
        }
        return Result.error("更新失败");
    }

    @RequestMapping(value = "/updateAvatar", method = RequestMethod.PATCH)
    public Result<String> updateAvatar(@RequestParam @URL String avatarUrl) {
        userService.updateAvatar(avatarUrl);
        return Result.success();
    }

    @RequestMapping(value = "/updatePwd", method = RequestMethod.PATCH)
    public Result<String> updatePwd(@RequestBody Map<String, String> params, @RequestHeader("Authorization") String token) {
        // 1.校验参数
        String oldPwd = params.get("old_pwd");
        String newPwd = params.get("new_pwd");
        String rePwd = params.get("re_pwd");

        if (!StringUtils.hasLength(oldPwd) || !StringUtils.hasLength(newPwd) || !StringUtils.hasLength(rePwd)) {
            return Result.error("缺少必要的参数");
        }

        // newPwd和rePwd是否一样
        if (!rePwd.equals(newPwd)) {
            return Result.error("两次填写的新密码不一样");
        }

        // 不能与原密码一致
        if (oldPwd.equals(newPwd)) {
            return Result.error("新密码不能与原密码相同");
        }

        // newPwd的长度必须在5~16位之间
        if (newPwd.length() < 5 || newPwd.length() > 16) {
            return Result.error("新密码必须是5~16位非空字符");
        }

        // 原密码是否正确
        // 调用userService根据用户名拿到原密码，再和old_pwd比对
        Map<String, Object> map = ThreadLocalUtil.get();
        User loginUser = userService.findByUserName((String) map.get("username"));
        if (!loginUser.getPassword().equals(Md5Util.getMd5String(oldPwd))) {
            return Result.error("原密码填写不正确");
        }

        // 2.调用service完成密码更新
        userService.updatePwd(newPwd);
        // 删除redis中对应的token
        stringRedisTemplate.opsForValue().getOperations().delete(token);
        return Result.success();
    }
}
