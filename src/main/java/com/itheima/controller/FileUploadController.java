package com.itheima.controller;

import com.itheima.pojo.Result;
import com.itheima.utils.MinioUtil;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2024/4/17
 */
@RestController
@CrossOrigin
public class FileUploadController {
    /**
     * 上传文件
     */
    @PostMapping("/upload-local")
    public Result<String> fileUpload(MultipartFile file) throws IOException {
        // 获取文件内容的输入流，写入到本地磁盘文件
        String originalFilename = file.getOriginalFilename();
        String filename = UUID.randomUUID() + Objects.requireNonNull(originalFilename).substring(originalFilename.lastIndexOf("."));
        file.transferTo(new File("E:\\Desktop\\big_event\\upload\\" + filename));
        return Result.success("url访问地址...");
    }

    @PostMapping("/upload")
    public Result<String> fileUpload2(MultipartFile file) {
        String originalFilename = file.getOriginalFilename();
        String filename = UUID.randomUUID() + Objects.requireNonNull(originalFilename).substring(originalFilename.lastIndexOf("."));
        MinioUtil.uploadFile(MinioUtil.BUCKET_NAME, file, filename, file.getContentType());
        // "https://big-event-gwd.oss-cn-beijing.aliyuncs.com/9bf1cf5b-1420-4c1b-91ad-e0f4631cbed4.png"
        String url = MinioUtil.getPresignedObjectUrl(MinioUtil.BUCKET_NAME, filename);
        return Result.success(url);
    }
}
