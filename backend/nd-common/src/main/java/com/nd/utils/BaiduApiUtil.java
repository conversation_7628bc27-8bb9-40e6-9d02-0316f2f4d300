package com.nd.utils;

import okhttp3.*;
import java.util.concurrent.TimeUnit;
import com.alibaba.fastjson.JSONObject;
import java.io.*;
import com.nd.properties.BaiduApiProperties;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public class BaiduApiUtil {

    private final BaiduApiProperties baiduApiProperties;
    public static final OkHttpClient HTTP_CLIENT = new OkHttpClient().newBuilder().readTimeout(300, TimeUnit.SECONDS).build();

    /**
     * @param type 识别的类型 animal|plant
     * @param imageBase64 图片转换后的Base64码
     * @throws IOException
     */
    private void Identify(String type,String imageBase64) throws IOException {
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        RequestBody body = RequestBody.create(mediaType, "image=" + imageBase64);
        String url = baiduApiProperties.getUrl() + type+"?access_token="+getAccessToken();
        Request request = new Request.Builder()
                .url(url)
                .method("POST", body)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .addHeader("Accept", "application/json")
                .build();
        Response response = HTTP_CLIENT.newCall(request).execute();
        System.out.println(response.body().string());
    }

    private String getAccessToken() throws IOException {
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        RequestBody body = RequestBody.create(mediaType, "grant_type=client_credentials&client_id=" + baiduApiProperties.getApiKey()
                + "&client_secret=" + baiduApiProperties.getApiSecret());
        Request request = new Request.Builder()
                .url("https://aip.baidubce.com/oauth/2.0/token")
                .method("POST", body)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .build();
        Response response = HTTP_CLIENT.newCall(request).execute();
        JSONObject jsonResponse = JSONObject.parseObject(response.body().string());
        return jsonResponse.getString("access_token");
    }

}