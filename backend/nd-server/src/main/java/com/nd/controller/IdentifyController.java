package com.nd.controller;


import com.nd.enumeration.HttpStatus;
import com.nd.result.Result;
import com.nd.service.IdentifyService;
import com.nd.vo.IdentifyResVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/identify")
@AllArgsConstructor
@Api("图片识别相关接口")
public class IdentifyController {

    final IdentifyService identifyService;

    /**
     * 图片上传识别
     * @param file
     * @param type
     * @return
     */
    @PutMapping("/upload")
    @ApiOperation("图片上传识别")
    public Result<IdentifyResVO> upload(@RequestParam("file") MultipartFile file , @RequestParam String type) {
       log.info("文件:{},类型:{}", type, file.getOriginalFilename());
       return Result.success(identifyService.upload(file,type), HttpStatus.SUCCESS,"识别成功");
    }
}
