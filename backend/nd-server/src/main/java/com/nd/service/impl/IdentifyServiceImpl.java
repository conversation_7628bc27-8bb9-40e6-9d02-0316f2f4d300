package com.nd.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.nd.enumeration.IdentifyType;
import com.nd.result.Result;
import com.nd.service.IdentifyService;
import com.nd.utils.BaiduApiUtil;
import com.nd.vo.IdentifyResVO;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.SelectKey;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Base64;

/**
 * 图片识别服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class IdentifyServiceImpl implements IdentifyService {

    private final BaiduApiUtil baiduApiUtil;

    @Override
    @SneakyThrows
    public IdentifyResVO upload(MultipartFile file, IdentifyType type) {
        //1.将图片转换为Base64
        byte [] bytes = file.getBytes();
        String image = Base64.getEncoder().encodeToString(bytes);
        log.info(image);
        //2.调用接口
        JSONObject identifyResponse = baiduApiUtil.Identify(type.value(), image );
        log.info("识别结果{}", identifyResponse);
        IdentifyResVO identifyResVO = new IdentifyResVO();
        identifyResVO.setName(identifyResponse.getString("name"));
        identifyResVO.setScore(identifyResponse.getString("score"));
        return identifyResVO;
    }
}
