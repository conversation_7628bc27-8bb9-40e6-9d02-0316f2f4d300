package com.nd.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nd.entity.User;
import com.nd.entity.UserInfo;
import com.nd.enumeration.HttpStatus;
import com.nd.exceptions.LoginFailedException;
import com.nd.mapper.UserInfoMapper;
import com.nd.mapper.UserMapper;
import com.nd.properties.WeChatProperties;
import com.nd.result.Result;
import com.nd.service.UserService;
import com.nd.utils.JwtUtil;
import com.nd.vo.UserInfoVO;
import lombok.AllArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.nd.constant.BaseContext.*;

/**
 * 用户服务实现类
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    private final WeChatProperties properties;
    private final UserMapper userMapper;
    private final UserInfoMapper userInfoMapper;
    private final RedisTemplate<String, String> stringRedisTemplate;

    /*
     * 实现登录逻辑
     **/
    @Override
    public Result<UserInfoVO> login(String code) {
        String url = String.format(properties.getUrl(), code);
        String res = HttpUtil.get(url);
        Map<String, Object> resultForWechat = JSONUtil.parseObj(res);
        // 获取 session_key(会话信息)、openid(用户唯一标识)
        String sessionKey = (String) resultForWechat.get("session_key");
        String openId = (String) resultForWechat.get("openid");
        UserInfoVO userInfoVO = new UserInfoVO();
        
        if (!StrUtil.isBlank(sessionKey) && !StrUtil.isBlank(openId)) {
            String token = JwtUtil.genToken(resultForWechat);
            userInfoVO.setToken(token);

            // Check if user exists in UserInfo table
            QueryWrapper<UserInfo> userInfoQuery = new QueryWrapper<UserInfo>().eq("openid", openId);
            if (1 == userInfoMapper.selectCount(userInfoQuery)) {
                // 用户已存在,更新用户信息
                UserInfo userInfo = userInfoMapper.selectOne(userInfoQuery);
                userInfoVO.setNickname(userInfo.getNickname());
                userInfoVO.setAvatar(userInfo.getAvatar());
            } else {
                // 用户不存在,新增用户
                try {
                    insertUserIfNotExists(openId, token);
                    // 保存到redis中
                    stringRedisTemplate.opsForValue().set(openId, token);
                }catch (Exception e) {
                    throw new LoginFailedException(LOGIN_FAILED);
                }finally {
                    userInfoVO.setNickname(DEFAULT_NICKNAME);
                    userInfoVO.setAvatar(DEFAULT_AVATAR);
                }
            }
            return Result.success(userInfoVO, HttpStatus.SUCCESS, LOGIN_SUCCESS);
        }
        throw new LoginFailedException(LOGIN_FAILED);
    }

    @Override
    public void logout() {

    }

    /**
     * 保存用户信息到数据库
     * @param openId
     * @param token
     */
    @Transactional
    protected void insertUserIfNotExists(String openId, String token) {
        // 检查用户表中是否存在当前用户
        QueryWrapper<User> userQuery = new QueryWrapper<User>().eq("openid", openId);
        if (userMapper.selectCount(userQuery) == 0) {
            // 如果没有就插入
            User userEntity = new User();
            userEntity.setOpenid(openId);
            userMapper.insert(userEntity);
        }
        
        // 检查用户信息表中是否存在当前用户
        QueryWrapper<UserInfo> userInfoQuery = new QueryWrapper<UserInfo>().eq("openid", openId);
        if (userInfoMapper.selectCount(userInfoQuery) == 0) {
            // 如果没有就插入
            UserInfo userInfo = new UserInfo();
            userInfo.setOpenid(openId);
            userInfo.setNickname(DEFAULT_NICKNAME);
            userInfo.setAvatar(DEFAULT_AVATAR);
            userInfoMapper.insert(userInfo);
        }
    }
}
